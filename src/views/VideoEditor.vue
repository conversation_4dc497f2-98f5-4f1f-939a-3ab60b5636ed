<template>
  <div class="video-editor">
    <!-- 添加 VideoEditorHeader 组件 -->
    <div class="video-editor-header">
      <VideoEditorHeader :default-aspect-ratio="aspectRatio" :canvas-name="canvasName" :canvas-id="canvasId"
        @aspect-ratio-change="onAspectRatioChange" @export="handleExport" @project-change="handleProjectChange"
        @project-switch="handleProjectSwitch" />
    </div>

    <!-- 三栏布局 -->
    <div class="video-editor-layout">
      <!-- 左侧面板：图片视频生成区域 -->
      <div class="left-panel" :class="{ 'collapsed': isLeftPanelCollapsed }">
        <div class="panel-content">
          <div class="panel-body">
            <VideoGenerationPanel v-if="currentShot && shotList.length > 0"
              :key="`generation-${currentShot.id || currentShotIndex}`" :shot="currentShot" @update:shot="updateShot"
              :voices="voices" :isLoadingVoices="isLoadingVoices" @refresh-voices="fetchVoices"
              @refresh-canvas="handleRefreshCanvas" :active-tab-from-parent="activeGenerationTab"
              @tab-change="handleGenerationPanelTabChange" :canvas-id="canvasId" />



          </div>
        </div>
        <!-- 收起/展开按钮 -->
        <div class="collapse-button left" @click="toggleLeftPanel">
          <el-icon v-if="isLeftPanelCollapsed">
            <ArrowRight />
          </el-icon>
          <el-icon v-else>
            <ArrowLeft />
          </el-icon>
        </div>
      </div>

      <!-- 中间区域：预览和分镜列表 -->
      <div class="center-panel">
        <!-- 媒体编辑菜单区域 -->
        <div class="media-editor-menu-container" :class="{ 'collapsed': isPlaying }">
          <MediaEditorMenu :current-shot="currentShot" @update="updateShot" @refresh="handleRefreshCanvas" />
        </div>

        <!-- 上部分：预览区域 -->
        <div class="preview-area">
          <div class="preview-container" v-if="!isVideoEditorCollapsed">
            <VideoEditorPreview ref="previewComponent" :shots="shotList" :default-aspect-ratio="aspectRatio"
              :start-index="currentShotIndex" @time-update="onTimeUpdate" @shot-change="onShotChange"
              @play-state-change="onPlayStateChange" @playback-completed="onPlaybackCompleted"
              :show-subtitles="showSubtitles" />
          </div>
        </div>

        <!-- 下部分：分镜列表 processAudioData -->
        <div class="shot-list-area" :class="{ 'collapsed': isShotListCollapsed }">
          <VideoShotList :shots="shotList" v-model="currentShotIndex" @add="addNewShot" @remove="removeShot"
            @shot-select="onShotSelect" class="shot-list-container" @reorder="updateShotOrder" :view-mode="shotViewMode"
            :current-playback-time="currentPlaybackTime" :current-shot-index="currentShotIndex" :is-playing="isPlaying"
            v-model:timelineScale="timelineScale" v-model:bgAudioTrack="bgAudioTrack"
            @update:totalDuration="handleTotalDurationChange" :aspectRatio="aspectRatio"
            @audio-track-select="handleAudioTrackSelect" :setBackgroundMusic="handleSetBackgroundMusic" />
          <!-- 收起/展开按钮 -->
          <div class="collapse-button bottom" @click="toggleShotList">
            <el-icon v-if="isShotListCollapsed">
              <ArrowUp />
            </el-icon>
            <el-icon v-else>
              <ArrowDown />
            </el-icon>
          </div>
        </div>

        <!-- 播放控制区域 -->
        <VideoPlaybackControls :is-playing="isPlaying" :current-playback-time="currentPlaybackTime"
          :total-playback-duration="totalPlaybackDuration" v-model:show-subtitles="showSubtitles"
          v-model:shot-view-mode="shotViewMode" v-model:timeline-scale="timelineScale" :current-shot="currentShot"
          :background-music="bgAudioTrack" @play="togglePlayPause" @pause="togglePlayPause"
          @toggle-fullscreen="toggleFullScreen" @update:backgroundMusicVolume="handleBackgroundMusicVolumeChange"
          @update:videoVolume="handleVideoVolumeChange" @update:audioVolume="handleAudioVolumeChange" />


      </div>

      <!-- 右侧面板：素材管理区域 -->
      <div class="right-panel" :class="{ 'collapsed': isRightPanelCollapsed }">
        <div class="panel-content">
          <div class="panel-body">
            <VideoAssetsPanel :is-loading="isAssetsLoading" @select-asset="selectAsset" @upload="uploadAsset"
              :active-tab-from-parent="activeAssetType" @tab-change="handleAssetsPanelTabChange" :canvas-id="canvasId"
              ref="assetsPanelRef" />
          </div>
        </div>
        <!-- 收起/展开按钮 -->
        <div class="collapse-button right" @click="toggleRightPanel">
          <el-icon v-if="isRightPanelCollapsed">
            <ArrowLeft />
          </el-icon>
          <el-icon v-else>
            <ArrowRight />
          </el-icon>
        </div>
      </div>
    </div>

    <!-- 添加 ReferenceSelector 引导组件 -->
    <ReferenceSelector v-model:visible="showReferenceSelector" :canvas-id="canvasId" title="添加素材到草稿"
      @select="handleReferenceSelect" @upload-success="handleReferenceUploadSuccess" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, inject, onBeforeUnmount } from 'vue';
import { ArrowLeft, ArrowRight, Plus, Delete, VideoCamera, ArrowUp, ArrowDown, Close, Picture } from '@element-plus/icons-vue';
import { ElMessage, ElSwitch, ElLoading } from 'element-plus';
import VideoEditorPreview from '@/components/videoEdit/VideoEditorPreview.vue';
import VideoShotList from '@/components/videoEdit/VideoShotList.vue';
import VideoGenerationPanel from '@/components/videoEdit/VideoGenerationPanel.vue';
import VideoAssetsPanel from '@/components/videoEdit/VideoAssetsPanel.vue';
import VideoEditorHeader from '@/components/videoEdit/VideoEditorHeader.vue';
import MediaEditorMenu from '@/components/videoEdit/MediaEditorMenu.vue';
import VideoPlaybackControls from '@/components/videoEdit/VideoPlaybackControls.vue';
import DropdownPanel from '@/components/parent/DropdownPanel.vue';
import ReferenceSelector from '@/components/videoEdit/ReferenceSelector.vue'; // 引入 ReferenceSelector 组件

import { updateShotVolume, setBackgroundMusic, getUserProfile, getVoiceList, getCanvasDetail, createCanvasShot, deleteCanvasShot, editCanvasShot, updateCanvasShotOrder, shotStatusBatch, updateCanvas } from '@/api/auth.js';
import { useRoute } from 'vue-router';
// 导入音频服务
import audioService from '@/services/audioService.js';
// 导入背景音乐播放服务
import audioBackgroundMusicService from '@/services/AudioBackgroundMusicService.js';

// 从App.vue注入积分相关的变量和函数
const userPoints = inject('userPoints', 0);
const updateUserPoints = inject('updateUserPoints', () => { });

// 面板折叠状态
const isLeftPanelCollapsed = ref(true);
const isRightPanelCollapsed = ref(true);
const isShotListCollapsed = ref(true); // 分镜列表折叠状态
const isVideoEditorCollapsed = ref(true); // 视频编辑器折叠状态

// ReferenceSelector 显示控制
const showReferenceSelector = ref(false);

// 分镜视图模式（'default' 或 'timeline'）
const shotViewMode = ref('default'); // 默认为时间线视图
// 记录播放前的视图模式，用于播放结束后恢复
const previousViewMode = ref('default');

// 获取路由参数
const route = useRoute();
const canvasId = computed(() => route.query.canvasId);

// 画布名称
const canvasName = ref('');

// 画布封面图片
const coverImage = ref('');

const isAssetsLoading = ref(false);
// 素材面板激活的标签页
const activeAssetType = ref('project');

// 添加音色相关状态变量
const voices = ref([]);
const isLoadingVoices = ref(false);

// 播放状态
const isPlaying = ref(false);
const currentPlaybackTime = ref(0);
const totalPlaybackDuration = ref(0);

// 字幕显示控制
const showSubtitles = ref(true);

// 当前选中的音轨索引
const selectedAudioTrackIndex = ref(0);

// 画布比例
const aspectRatio = ref('16:9');

// 当前生成面板激活的标签页
const activeGenerationTab = ref('video');

// 分镜列表数据
const shotList = ref([]);

// 当前选中的分镜索引
const currentShotIndex = ref(0);

// 背景音乐轨道数据
const bgAudioTrack = ref(null);

// 时间轴缩放比例
const timelineScale = ref(32);

// 处理音轨选择变更
const handleAudioTrackSelect = (trackIndex) => {
  console.log('选中音轨索引:', trackIndex);
  selectedAudioTrackIndex.value = trackIndex;
};

// 处理总时长变化
const handleTotalDurationChange = (newDuration) => {
  // 更新视图中的总时长
  totalPlaybackDuration.value = newDuration / 1000; // 转换为秒
};

// 轮询相关状态变量
const pollInterval = ref(5000); // 轮询间隔，默认5秒
const pollTimer = ref(null); // 轮询定时器ID
const pendingShotIds = ref([]); // 待处理的分镜ID列表
const isPolling = ref(false); // 是否正在轮询

// 当前选中的分镜
const currentShot = computed(() => {
  return shotList.value[currentShotIndex.value] || null;
});

// 切换左侧面板折叠状态
const toggleLeftPanel = () => {
  isLeftPanelCollapsed.value = !isLeftPanelCollapsed.value;

  if (previewComponent.value && !isLeftPanelCollapsed.value) {
    if (isPlaying.value) {
      previewComponent.value.pause();
    }
  }
};

// 切换右侧面板折叠状态
const toggleRightPanel = () => {
  isRightPanelCollapsed.value = !isRightPanelCollapsed.value;

  if (previewComponent.value && !isRightPanelCollapsed.value) {
    if (isPlaying.value) {
      previewComponent.value.pause();
    }
  }
};

// 切换分镜列表折叠状态
const toggleShotList = () => {
  isShotListCollapsed.value = !isShotListCollapsed.value;
};

// 监听视图模式变化
watch(shotViewMode, (newMode) => {
  // 如果不是在播放状态，也更新 previousViewMode
  if (!isPlaying.value) {
    previousViewMode.value = newMode;
  }
});

// 添加新分镜
const addNewShot = async (index) => {

  try {
    // 如果有画布ID，则调用API创建分镜
    if (canvasId.value) {
      // ElMessage.info('正在创建新分镜...');

      const params = {
        canvasId: Number(canvasId.value),
        insertPosition: index + 2 // API中的插入位置
      };

      const response = await createCanvasShot(params);

      if (response.success) {
        const shotId = response.data;
        console.log('创建分镜成功，ID:', shotId);

        // 使用防抖的刷新函数
        await handleRefreshCanvas();
        currentShotIndex.value = index + 1;

        // 更新音轨数据
        calculateTotalDuration();


        // // 使用返回的分镜ID更新本地分镜对象
        // newShot.id = shotId;
        // newShot.canvasId = Number(canvasId.value);

        // // 将新分镜添加到本地列表
        // shotList.value.splice(index + 1, 0, newShot);
        // currentShotIndex.value = index + 1;

        // ElMessage.success('分镜创建成功');
      } else {
        console.error('创建分镜失败:', response.errMessage);
        ElMessage.error(`创建分镜失败: ${response.errMessage}`);
        return; // 创建失败，不更新本地数据
      }
    }
    // else {
    //   // 没有画布ID，只在本地添加（测试模式）
    //   console.warn('没有画布ID，只在本地添加分镜（测试模式）');
    //   newShot.id = `shot-${Date.now()}`; // 本地临时ID
    //   shotList.value.splice(index + 1, 0, newShot);
    //   currentShotIndex.value = index + 1;
    // }

    // // 更新总时长
    // if (previewComponent.value) {
    //   totalPlaybackDuration.value = previewComponent.value.getTotalDuration();
    // }

    // isLeftPanelCollapsed.value = shotList.value.length > 0 ? false : true;
    // isRightPanelCollapsed.value = shotList.value.length > 0 ? false : true;
  } catch (error) {
    console.error('创建分镜异常:', error);
    ElMessage.error('创建分镜出错，请稍后再试');
  }
};

// 删除分镜
const removeShot = async (index) => {
  // 获取要删除的分镜
  const shotToRemove = shotList.value[index];

  try {
    // 如果分镜有ID且不是本地临时ID（以"shot-"开头），则调用API删除
    if (shotToRemove.id && (!String(shotToRemove.id).startsWith('shot-'))) {
      // ElMessage.info('正在删除分镜...');

      const response = await deleteCanvasShot(shotToRemove.id);

      if (response.success) {
        console.log('删除分镜成功，ID:', shotToRemove.id);
        // ElMessage.success('分镜删除成功');
        if (canvasId.value) {
          fetchCanvasDetail(canvasId.value);
        }
      } else {
        console.error('删除分镜失败:', response.errMessage);
        ElMessage.error(`删除分镜失败: ${response.errMessage}`);
        return; // 删除失败，不更新本地数据
      }
    } else {
      // 本地临时分镜，无需调用API
      console.warn('删除本地临时分镜，无需调用API');
    }

    // 删除本地分镜数据
    shotList.value.splice(index, 1);

    // 如果删除的是当前选中的分镜，则选中前一个分镜
    if (currentShotIndex.value >= shotList.value.length) {
      currentShotIndex.value = shotList.value.length - 1;
    }

    // 更新总时长
    if (previewComponent.value) {
      totalPlaybackDuration.value = previewComponent.value.getTotalDuration();
    }

    // 更新音轨数据
    calculateTotalDuration();

    isLeftPanelCollapsed.value = shotList.value.length > 0 ? false : true;
    isRightPanelCollapsed.value = shotList.value.length > 0 ? false : true;
  } catch (error) {
    console.error('删除分镜异常:', error);
    ElMessage.error('删除分镜出错，请稍后再试');
  }
};

// 更新分镜数据
const updateShot = async (updatedShot) => {
  // 获取当前分镜
  const currentShotData = shotList.value[currentShotIndex.value];

  // 使用扩展运算符创建一个新对象，确保所有属性都被正确复制
  const updatedShotData = { ...currentShotData, ...updatedShot };
  shotList.value[currentShotIndex.value] = updatedShotData;

  // 如果预览组件存在，需要重新加载当前分镜
  if (previewComponent.value) {
    // 通知预览组件重新加载当前分镜
    previewComponent.value.loadCurrentShot();

    // 更新总时长
    totalPlaybackDuration.value = previewComponent.value.getTotalDuration();

    // 更新音轨数据
    calculateTotalDuration();
  }

  // 如果分镜有ID且不是本地临时ID（以"shot-"开头），则调用API保存
  if (updatedShotData.id && (!String(updatedShotData.id).startsWith('shot-'))) {
    try {

      // 调用API保存分镜
      saveShot(updatedShotData);
    } catch (error) {
      console.error('准备保存分镜数据时出错:', error);
    }
  } else {
    console.warn('分镜是本地临时分镜，不调用API保存');
  }
};

// 使用防抖保存分镜数据
let saveTimeout = null;
const saveShot = (params) => {
  // 清除之前的定时器
  if (saveTimeout) {
    clearTimeout(saveTimeout);
  }

  // 设置新的定时器，500ms后执行保存
  saveTimeout = setTimeout(async () => {
    try {
      console.log('保存分镜数据:', params);
      const response = await editCanvasShot(params);

      if (response.success) {
        console.log('保存分镜成功');
        // 不显示成功消息，避免频繁打扰用户
      } else {
        console.error('保存分镜失败:', response.errMessage);
        ElMessage.error(`保存分镜失败: ${response.errMessage}`);
      }
    } catch (error) {
      console.error('保存分镜异常:', error);
      ElMessage.error('保存分镜出错，请稍后再试');
    }

    if (canvasId.value) {
      fetchCanvasDetail(canvasId.value);
    }
  }, 500);
};

// 处理比例变化
const onAspectRatioChange = (ratio) => {
  aspectRatio.value = ratio;
  console.log('画布比例已更新为:', ratio);

  // 如果需要，可以在这里更新当前分镜的比例信息
  // if (currentShot.value) {
  //   currentShot.value.aspectRatio = ratio;
  // }

  updateCanvas({
    canvasId: canvasId.value,
    ratio: aspectRatio.value
  });
};

// 时间更新事件处理
const onTimeUpdate = ({ currentTime, duration, shotIndex, isPlaying: eventIsPlaying }) => {
  // console.log('当前时间:', currentTime, '总时长:', duration, '分镜索引:', shotIndex);
  currentPlaybackTime.value = currentTime;
  totalPlaybackDuration.value = duration; // 更新总时长

  // 如果事件中包含播放状态，则使用事件中的播放状态，否则使用当前组件的播放状态
  const playState = eventIsPlaying !== undefined ? eventIsPlaying : isPlaying.value;

  // 更新背景音乐播放状态
  audioBackgroundMusicService.updatePlaybackState(currentTime * 1000, playState);

};

// 分镜变更事件处理
const onShotChange = (shotIndex) => {
  console.log('分镜变更:', shotIndex);
  currentShotIndex.value = shotIndex;

  // 如果正在播放，强制检查音频轨道
  if (isPlaying.value) {
    // 计算当前播放时间
    const currentTime = currentPlaybackTime.value;

    // 强制检查音频轨道
    console.log('分镜变更，强制检查音频轨道:', currentTime);
  }
};

// 播放状态变更事件处理
const onPlayStateChange = (playing) => {
  console.log('播放状态变更:', playing ? '播放中' : '已暂停');
  isPlaying.value = playing;

  isLeftPanelCollapsed.value = playing;
  isRightPanelCollapsed.value = playing;

  // 处理视图模式切换
  if (playing) {
    // 播放开始时，记录当前视图模式并切换到时间轴视图
    previousViewMode.value = shotViewMode.value;
    if (shotViewMode.value !== 'timeline') {
      shotViewMode.value = 'timeline';
    }

    // 确保音频播放服务知道当前正在播放
    // 这里不需要立即调用 checkAndPlayAudioTracks，因为 VideoEditorPreview 组件会在播放开始时触发 time-update 事件
  } else {
    // 播放暂停时，如果之前是默认视图，则恢复
    if (previousViewMode.value === 'default') {
      shotViewMode.value = 'default';
    }
  }
};

// 播放完成事件处理
const onPlaybackCompleted = () => {
  console.log('所有分镜播放完成');
  ElMessage.info('视频播放完成');
  isPlaying.value = false;
  isLeftPanelCollapsed.value = false;
  isRightPanelCollapsed.value = false;

  // 恢复到播放前的视图模式
  if (previousViewMode.value === 'default') {
    shotViewMode.value = 'default';
  }
};

// 切换播放/暂停
const togglePlayPause = () => {
  if (previewComponent.value) {
    if (isPlaying.value) {
      previewComponent.value.pause();
    } else {
      previewComponent.value.play();
    }
  }
};

// 切换全屏
const toggleFullScreen = () => {
  if (previewComponent.value) {
    previewComponent.value.toggleFullscreen();
  }
};

// 播放下一个分镜
const playNextShot = () => {
  console.log('尝试播放下一个分镜, previewComponent:', previewComponent.value);

  if (!previewComponent.value) {
    console.error('预览组件引用不存在，无法播放下一个分镜');
    return;
  }

  if (currentShotIndex.value >= shotList.value.length - 1) {
    console.log('已经是最后一个分镜，无法播放下一个');
    return;
  }

  try {
    console.log('调用预览组件的playNextShot方法');
    previewComponent.value.playNextShot();
  } catch (error) {
    console.error('播放下一个分镜时出错:', error);
  }
};

// 播放上一个分镜
const playPreviousShot = () => {
  console.log('尝试播放上一个分镜, previewComponent:', previewComponent.value);

  if (!previewComponent.value) {
    console.error('预览组件引用不存在，无法播放上一个分镜');
    return;
  }

  if (currentShotIndex.value <= 0) {
    console.log('已经是第一个分镜，无法播放上一个');
    return;
  }

  try {
    console.log('调用预览组件的playPreviousShot方法');
    previewComponent.value.playPreviousShot();
  } catch (error) {
    console.error('播放上一个分镜时出错:', error);
  }
};

// formatTime 函数已移至 VideoPlaybackControls 组件中

// 选择素材
const selectAsset = ({ type, asset }) => {
  if (!currentShot.value) return;

  // 主要根据 materialType 字段来决定处理逻辑
  if (asset.materialType === 1) {
    // 图片素材
    updateShot({
      imageUrl: asset.materialUrl,
      imageStatus: 'COMPLETED',
      imageAspectRatio: asset.imageMaterialParams?.aspectRatio || '16:9',
      referenceImage: asset.imageMaterialParams?.referenceImageUrl || asset.materialUrl,
      movement: asset.movement || '',
      imagePrompt: asset.imageMaterialParams?.prompt || '',
      startFrameImage: asset.materialUrl,
      type: 'image'
    });
  } else if (asset.materialType === 2) {
    // 视频素材
    updateShot({
      videoUrl: asset.materialUrl,
      videoDuration: asset.videoMaterialParams?.duration || 0,
      videoAspectRatio: asset.videoMaterialParams?.ratio || '16:9',
      startFrameImage: asset.videoMaterialParams?.firstFrameUrl || '',
      endFrameImage: asset.videoMaterialParams?.lastFrameUrl || '',
      videoPrompt: asset.videoMaterialParams?.prompt || '',
      type: 'video'
    });
  } else if (asset.materialType === 3) {
    // 音频素材，添加到背景音乐
    console.log('选择了音频素材:', asset);

    // 检查音频素材的URL是否有效
    if (!asset.materialUrl) {
      console.error('音频素材URL无效:', asset);
      ElMessage.error('音频素材URL无效，无法添加');
      return;
    }

    // 添加到背景音乐
    addAudioToBgTrack(asset).catch(error => {
      console.error('添加背景音乐失败:', error);
      ElMessage.error('添加背景音乐失败，请重试');
    });

  }
};

// 添加音频到背景音乐轨道
const addAudioToBgTrack = async (audioAsset) => {
  console.log('开始添加背景音乐素材:', audioAsset);

  // 显示加载中
  const loading = ElLoading.service({
    lock: true,
    text: '处理背景音乐中...',
    background: 'rgba(0, 0, 0, 0.7)'
  });

  try {
    // 获取音频时长
    let audioDuration = audioAsset.materialDuration;
    console.log('素材中的音频时长 materialDuration:', audioDuration);

    // 如果materialDuration不存在或不是有效数字，尝试使用音频服务获取实际时长
    if (!audioDuration || isNaN(audioDuration)) {
      console.log('materialDuration无效，尝试使用Audio API获取时长');
      audioDuration = await audioService.getAudioDuration(audioAsset.materialUrl);
      console.log('通过Audio API获取到的音频时长(毫秒):', audioDuration);
    }

    // 创建新的背景音乐片段
    const newBgClip = {
      canvasId: canvasId.value,
      id: `bg-clip-${audioAsset.id || Date.now()}`,
      name: audioAsset.materialName || "背景音乐",
      audioUrl: audioAsset.materialUrl,
      audioDuration: audioDuration,
      startTime: 0,
      endTime: audioDuration,
      startTrackTime: 0,
      volume: 1.0,
    };
    console.log('创建的新背景音乐片段:', newBgClip);

    const response = await setBackgroundMusic(newBgClip);
    console.log('设置背景音乐结果:', response);

    if (response.success) {
      ElMessage.success('设置背景音乐成功');
      // 更新分镜
      fetchCanvasDetail(canvasId.value);

      // 设置背景音乐轨道
      // bgAudioTrack.value = newBgClip;
    }

    // 检查是否需要扩展总时长
    // if (audioDuration > totalPlaybackDuration.value * 1000) {
    //   totalPlaybackDuration.value = (audioDuration + 5000) / 1000;
    //   console.log('更新总时长为:', totalPlaybackDuration.value);
    // }

    ElMessage.success(`已添加背景音乐: ${newBgClip.name}`);
  } catch (error) {
    console.error('添加背景音乐失败:', error);
    ElMessage.error('添加背景音乐失败，请重试');
  } finally {
    // 关闭加载中
    loading.close();
  }
};

// 处理背景音乐设置API调用
const handleSetBackgroundMusic = async (bgClipData) => {
  console.log('VideoEditor: 开始调用背景音乐设置API:', bgClipData);

  try {
    // 构建API参数
    const params = {
      ...bgClipData,
      canvasId: canvasId.value,
      volume: bgClipData ? bgClipData.volume : undefined, // 转换为0-1范围
    };

    console.log('调用setBackgroundMusic API，参数:', params);
    const response = await setBackgroundMusic(params);
    console.log('背景音乐设置API响应:', response);

    if (response.success) {
      console.log('背景音乐设置成功');
      // 更新背景音乐服务
      audioBackgroundMusicService.setBackgroundMusic(bgClipData);
      // 可以选择是否刷新画布详情
      // fetchCanvasDetail(canvasId.value);
    } else {
      console.error('背景音乐设置失败:', response.message);
    }
  } catch (error) {
    console.error('调用背景音乐设置API失败:', error);
  }
};

// 处理背景音乐音量变化
const handleBackgroundMusicVolumeChange = async (volume) => {
  console.log('背景音乐音量变化:', volume);

  if (bgAudioTrack.value) {
    // 更新本地背景音乐数据
    const updatedBgMusic = {
      ...bgAudioTrack.value,
      volume: volume
    };

    // 调用API更新背景音乐
    await handleSetBackgroundMusic(updatedBgMusic);

    // 更新本地状态
    bgAudioTrack.value = updatedBgMusic;
  }
};

// 处理视频音量变化
const handleVideoVolumeChange = async (volume) => {
  console.log('视频音量变化:', volume);

  // 构建API参数
  const params = {
    shotId: currentShot.value?.id || '',
    type: 'video',
    volume: volume, // 转换为0-1范围
  };
  await updateShotVolume(params)

  // 调用updateShot方法更新分镜
  await updateShot(updatedShot);
};

// 处理音频音量变化（整体音量）
const handleAudioVolumeChange = async (volume) => {
  console.log('整体音频音量变化:', volume);

  if (currentShot.value && currentShot.value.audios && currentShot.value.audios.length > 0) {
    // 为所有音频设置相同的音量
    const updatedAudios = currentShot.value.audios.map(audio => ({
      ...audio,
      volume: volume
    }));

    // 更新当前分镜的音频数据
    const updatedShot = {
      ...currentShot.value,
      audios: updatedAudios
    };

    // 调用updateShot方法更新分镜
    await updateShot(updatedShot);
  }
};

// 上传素材
const uploadAsset = (assetType) => {
  ElMessage.info(`上传${assetType}功能待实现`);
  // 这里可以添加实际的上传逻辑
};

// 处理项目变更
const handleProjectChange = (project) => {
  console.log('切换到项目:', project.name);
  ElMessage.success(`已切换到项目: ${project.name}`);
  // 这里可以添加实际的项目切换逻辑
  fetchCanvasDetail(project.id);
};

// 处理导出
const handleExport = () => {
  console.log('开始导出视频');
};

// 选择分镜
const onShotSelect = (shotIndex, type) => {
  console.log('选择分镜:', shotIndex, '类型:', type);
  currentShotIndex.value = shotIndex;

  // 确保 type 是有效的值
  if (type && ['image', 'video', 'audio'].includes(type)) {
    activeGenerationTab.value = type;
  } else {
    // 默认使用图片生成标签页
    activeGenerationTab.value = 'video';
  }

  // 将素材面板切换到分镜素材标签页
  // activeAssetType.value = 'storyboard';

  // 如果有预览组件引用，跳转到选中的分镜
  if (previewComponent.value) {
    previewComponent.value.jumpToShot(shotIndex);
  }
};

// 添加预览组件引用
const previewComponent = ref(null);

// 组件挂载
onMounted(() => {
  // 添加窗口大小变化监听器
  window.addEventListener('resize', handleWindowResize);

  // 添加键盘事件监听器
  window.addEventListener('keydown', handleKeyDown);

  // 初始化背景音乐播放服务
  audioBackgroundMusicService.setDebugMode(false); // 设置为true可以在控制台查看详细日志

  // 如果URL中有canvasId参数，则获取画布详情
  if (canvasId.value) {
    fetchCanvasDetail(canvasId.value);
  }

  // 获取音色列表
  fetchVoices();
});

// 组件卸载前清理资源
onBeforeUnmount(() => {
  // 移除窗口大小变化监听器
  window.removeEventListener('resize', handleWindowResize);

  // 移除键盘事件监听器
  window.removeEventListener('keydown', handleKeyDown);

  // 清除轮询定时器
  stopPollingShotStatus();

  // 清理背景音乐服务
  audioBackgroundMusicService.destroy();
});

// 处理键盘事件
const handleKeyDown = (event) => {
  console.log('键盘事件:', event.key);

  // 检查当前焦点是否在输入框、文本区域或其他可编辑元素中
  const activeElement = document.activeElement;
  const isInput = activeElement.tagName === 'INPUT' ||
    activeElement.tagName === 'TEXTAREA' ||
    activeElement.isContentEditable;
  if (isInput) {
    return;
  }

  // 检查是否按下空格键 (keyCode 32 或 key ' ')
  if (event.key === ' ' || event.keyCode === 32) {
    // 如果不是在输入框中，则触发播放/暂停
    if (!isInput) {
      event.preventDefault(); // 阻止默认行为（如页面滚动）
      togglePlayPause();
    }
  } else if (event.key === 'Escape' && isPlaying.value) {
    event.preventDefault(); // 阻止默认行为（如页面滚动）
    togglePlayPause();
  }

  if (event.key === 'ArrowLeft') {
    playPreviousShot()
  } else if (event.key === 'ArrowRight') {
    playNextShot()
  } else if (event.key === 'Escape') {

  }

};

// 监听画布ID变化
watch(() => canvasId.value, (newId) => {
  if (newId) {
    fetchCanvasDetail(newId);
  }
});

// 监听shots变化，更新总时长
watch(() => shotList.value, () => {
  if (previewComponent.value) {
    totalPlaybackDuration.value = previewComponent.value.getTotalDuration();
  }
}, { deep: true });

// 监听背景音乐轨道变化
watch(() => bgAudioTrack.value, (newBgAudioTrack) => {
  console.log('背景音乐轨道变化:', newBgAudioTrack);
  // 更新背景音乐服务
  audioBackgroundMusicService.setBackgroundMusic(newBgAudioTrack);
}, { deep: true });

// 处理窗口大小变化
const handleWindowResize = () => {
  // 在这里添加处理窗口大小变化的逻辑
  console.log('窗口大小变化:', window.innerWidth, window.innerHeight)
};

// 获取音色列表
const fetchVoices = async () => {
  try {
    isLoadingVoices.value = true;
    const response = await getVoiceList();
    if (response.success) {
      // 处理音色数据
      voices.value = response.data;
      console.log('获取音色列表成功:', voices.value.length);
    } else {
      console.error('获取音色列表失败:', response.data?.errMessage);
    }
  } catch (error) {
    console.error('获取音色列表异常:', error);
  } finally {
    isLoadingVoices.value = false;
  }
};

// 获取画布详情
const fetchCanvasDetail = async (id) => {
  if (!id) {
    console.log('没有提供画布ID，使用默认数据');
    return;
  }

  try {
    console.log('获取画布详情，ID:', id);
    const response = await getCanvasDetail(id);

    if (response.success) {
      const canvasData = response.data;

      // 更新画布名称
      if (canvasData && canvasData.canvasName) {
        canvasName.value = canvasData.canvasName;
      }

      if (canvasData && canvasData.coverImage) {
        coverImage.value = canvasData.coverImage;
      }

      if (canvasData && canvasData.ratio) {
        aspectRatio.value = canvasData.ratio;
      }

      // 更新画布信息
      if (canvasData.shots && canvasData.shots.length > 0) {
        // 保存当前选中的分镜索引，确保组件稳定性
        const previousShotIndex = currentShotIndex.value;

        // 使用API返回的分镜数据
        const newShotList = canvasData.shots.map(shot => ({
          ...shot,
          // 确保兼容性处理
          id: shot.id,
          canvasId: shot.canvasId,
          code: shot.code || '',
          originalShotId: shot.originalShotId || '',
          type: shot.type || '',
          imagePrompt: shot.imagePrompt || '',
          movement: shot.movement || '',
          imageUrl: shot.imageUrl || '',
          imageAspectRatio: shot.imageAspectRatio || aspectRatio.value,
          referenceImage: shot.referenceImage || '',
          imageStatus: shot.imageStatus || 'PENDING',
          videoPrompt: shot.videoPrompt || '',
          videoUrl: shot.videoUrl || '',
          videoDuration: shot.videoDuration || 0,
          videoAspectRatio: shot.videoAspectRatio || aspectRatio.value,
          videoVolume: shot.videoVolume !== undefined ? shot.videoVolume : 1.0, // 默认视频音量为1.0
          startFrameImage: shot.startFrameImage || '',
          endFrameImage: shot.endFrameImage || '',
          audios: (shot.audios || []).map(audio => ({
            ...audio,
            volume: audio.volume !== undefined ? audio.volume : 1.0 // 默认音频音量为1.0
          }))
        }));

        // 更新分镜列表，确保当前选中的分镜索引有效
        shotList.value = newShotList;

        // 确保当前分镜索引在有效范围内
        if (previousShotIndex >= newShotList.length) {
          currentShotIndex.value = Math.max(0, newShotList.length - 1);
        } else if (previousShotIndex < 0) {
          currentShotIndex.value = 0;
        } else {
          currentShotIndex.value = previousShotIndex;
        }

        console.log('currentShotIndex.value', currentShotIndex.value);

        // 设置背景音乐轨道
        if (canvasData.backgroundMusic) {
          bgAudioTrack.value = canvasData.backgroundMusic;
          // 初始化背景音乐服务
          audioBackgroundMusicService.setBackgroundMusic(canvasData.backgroundMusic);
        } else {
          // 清除背景音乐
          bgAudioTrack.value = null;
          audioBackgroundMusicService.setBackgroundMusic(null);
        }

        console.log('更新分镜列表:', shotList.value.length);

        // 检查是否有未完成状态的分镜
        const unfinishedShots = shotList.value.filter(shot => shot.shotStatus == '1');
        if (unfinishedShots.length > 0) {
          // 构建待处理的分镜ID列表
          pendingShotIds.value = unfinishedShots.map(shot => shot.id);
          console.log('发现未完成的分镜:', pendingShotIds.value);

          // 开始轮询分镜状态
          startPollingShotStatus();
        } else {
          // 停止轮询
          stopPollingShotStatus();
        }

        // 如果封面图片为空，则使用分镜列表中 imageUrl 存在的图片作为封面图片
        if (!coverImage.value) {
          const imageUrl = shotList.value.find(shot => shot.imageUrl)?.imageUrl;
          if (imageUrl) {
            coverImage.value = imageUrl;
            // updateCanvas
            const response = await updateCanvas({
              canvasId: canvasId.value,
              coverImage: imageUrl,
              ratio: aspectRatio.value
            });
            if (response.success) {
              coverImage.value = imageUrl;
            }
          }
        }

        // 如果有分镜，确保不显示引导组件
        showReferenceSelector.value = false;

        // 处理音频数据和计算总时长
        calculateTotalDuration();


      } else {
        shotList.value = [];
        // 停止轮询
        stopPollingShotStatus();

        // 如果没有分镜，显示引导组件
        showReferenceSelector.value = true;
      }

      isLeftPanelCollapsed.value = shotList.value.length <= 0;
      isRightPanelCollapsed.value = shotList.value.length <= 0;
      isShotListCollapsed.value = false; // 分镜列表折叠状态
      isVideoEditorCollapsed.value = false; // 视频编辑器折叠状态

    } else {
      console.error('获取画布详情失败:', response.errMessage);
      ElMessage.error(`获取画布详情失败: ${response.errMessage}`);
    }
  } catch (error) {
    console.error('获取画布详情异常:', error);
    ElMessage.error('获取画布详情出错，请稍后再试');

    // 在错误情况下，确保组件状态稳定
    if (shotList.value.length === 0) {
      showReferenceSelector.value = true;
      isLeftPanelCollapsed.value = true;
      isRightPanelCollapsed.value = true;
    }
  } finally {
    fetchUserProfile()
  }
};


const fetchUserProfile = async () => {
  try {
    const response = await getUserProfile()
    if (response.data) {
      updateUserPoints(response.data.points || 0);
    }
  } catch (error) {
    console.error('获取用户资料异常:', error);
  }
};

// 计算总时长
const calculateTotalDuration = () => {
  let duration = 0;
  for (const shot of shotList.value) {
    duration += getShotDuration(shot);
  }
  totalPlaybackDuration.value = duration / 1000; // 转换为秒
};

// 获取分镜时长
const getShotDuration = (shot) => {
  // 获取当前分镜所有语音的总时长
  let countVoiceDuration = 0;
  if (shot.audios && shot.audios.length > 0) {
    countVoiceDuration = shot.audios.reduce((acc, voice) => acc + (voice.audioDuration || 0), 0);
  }

  // 如果视频时长大于0且大于语音时长，返回视频时长
  if (shot.videoDuration > 0 && shot.videoDuration > countVoiceDuration) {
    return shot.videoDuration;
  } else if (countVoiceDuration > 0) {
    // 如果有语音时长，返回语音时长
    return countVoiceDuration;
  }

  // 最后使用默认时长
  return 5000; // 默认5秒
};

// 处理素材面板标签变化
const handleAssetsPanelTabChange = (tab) => {
  console.log('素材面板标签变化:', tab);
  activeAssetType.value = tab;
};

// handleTimelineScaleChange 方法已移至 VideoPlaybackControls 组件中

// 处理生成面板标签变化
const handleGenerationPanelTabChange = (tab) => {
  console.log('生成面板标签变化:', tab);
  activeGenerationTab.value = tab;
};

// 添加防抖标志，防止重复调用
const isRefreshing = ref(false);

// 处理刷新画布事件
const handleRefreshCanvas = async () => {
  console.log('handleRefreshCanvas', canvasId.value, isRefreshing.value);
  if (canvasId.value && !isRefreshing.value) {
    isRefreshing.value = true;
    try {
      await fetchCanvasDetail(canvasId.value);
    } finally {
      isRefreshing.value = false;
    }
  }
};

// 处理项目切换事件
const handleProjectSwitch = (newCanvasId) => {
  if (newCanvasId && newCanvasId !== canvasId.value) {
    fetchCanvasDetail(newCanvasId);
  }
};

// 开始轮询分镜状态
const startPollingShotStatus = () => {
  // 如果已经在轮询中，则不重复启动
  if (isPolling.value) {
    return;
  }

  isPolling.value = true;
  console.log('开始轮询分镜状态');

  // 立即执行一次轮询
  pollShotStatus();

  // 设置定时器，定期轮询
  pollTimer.value = setInterval(pollShotStatus, pollInterval.value);
};

// 停止轮询分镜状态
const stopPollingShotStatus = () => {
  if (pollTimer.value) {
    clearInterval(pollTimer.value);
    pollTimer.value = null;
  }
  isPolling.value = false;
  pendingShotIds.value = [];
  console.log('停止轮询分镜状态');
};

// 轮询分镜状态
const pollShotStatus = async () => {
  // 如果没有待处理的分镜，则停止轮询
  if (pendingShotIds.value.length === 0) {
    stopPollingShotStatus();
    return;
  }

  try {
    // console.log('轮询分镜状态:', pendingShotIds.value);
    const response = await shotStatusBatch({
      shotIds: pendingShotIds.value
    });

    if (response.success && response.data) {
      // console.log('轮询分镜状态成功:', response.data);

      // 检查是否有状态为处理中的分镜
      const completedShots = response.data.filter(item => item.shotStatus != '1');

      // 检查是否有状态为3失败的分镜
      const shibaiShots = response.data.filter(item => item.shotStatus === '3');

      if (shibaiShots.length > 0) {
        ElMessage.error('分镜生成失败');
      }

      if (completedShots.length > 0) {
        console.log('发现已完成的分镜:', completedShots.map(item => item.shotId));

        // ElMessage.success('分镜生成完成');

        // 更新画布详情
        if (canvasId.value) {
          await fetchCanvasDetail(canvasId.value);
        }
      } else {
        // 更新待处理的分镜ID列表（移除不存在的分镜）
        const validShotIds = response.data.map(item => item.shotId);
        pendingShotIds.value = pendingShotIds.value.filter(id => validShotIds.includes(id));

        // 如果没有待处理的分镜，则停止轮询
        if (pendingShotIds.value.length === 0) {
          stopPollingShotStatus();
        }
      }
    } else {
      console.error('轮询分镜状态失败:', response.errMessage);
    }
  } catch (error) {
    console.error('轮询分镜状态异常:', error);
  }
};


// 组件引用
const assetsPanelRef = ref(null);
// 处理 ReferenceSelector 选择事件
const handleReferenceSelect = (selectedAssets) => {
  console.log('选择的素材:', selectedAssets);
  if (selectedAssets && selectedAssets.length > 0) {
    // 选择素材后，自动创建一个新分镜
    addNewShot(0);
  }

  assetsPanelRef.value.refreshMaterials();
};

// 处理 ReferenceSelector 上传成功事件
const handleReferenceUploadSuccess = () => {
  console.log('素材上传成功');
  // // 刷新画布详情
  // if (canvasId.value) {
  //   // 选择素材后，自动创建一个新分镜
  //   addNewShot(0);
  // }

  assetsPanelRef.value.refreshMaterials();

  // isLeftPanelCollapsed.value = false;
  isRightPanelCollapsed.value = false;
  isShotListCollapsed.value = false;
  isVideoEditorCollapsed.value = false;

  // 选中当前分镜
  onShotSelect(currentShotIndex.value);
};

// 处理分镜顺序变更
const updateShotOrder = async (newShotList) => {

  // 如果没有画布ID，只更新本地排序
  if (!canvasId.value) {
    console.warn('没有画布ID，只更新本地排序（测试模式）');
    // shotList.value = [...newShotList];
    return;
  }

  try {
    console.log('更新分镜顺序:', newShotList);

    // 过滤出有效的分镜ID（非本地临时ID）
    const validShots = newShotList.filter(shot => shot.id && !String(shot.id).startsWith('shot-'));

    // 如果没有有效分镜，只更新本地排序
    if (validShots.length === 0) {
      shotList.value = [...newShotList];
      return;
    }

    // 构建API所需参数
    const params = {
      canvasId: Number(canvasId.value),
      shotOrders: validShots.map((shot, index) => ({
        id: shot.id,
        sortOrder: index + 1
      }))
    };

    // 更新本地数据的 sortOrder 字段
    shotList.value = newShotList.map((shot, index) => {
      shot.sortOrder = index + 1;
      return shot;
    });

    // 更新音轨数据
    calculateTotalDuration();

    // 调用API更新服务器端分镜顺序
    const response = await updateCanvasShotOrder(params);

    if (response.success) {
      console.log('更新分镜顺序成功');
      // 更新本地分镜列表
      // shotList.value = [...newShotList];

      // ElMessage.success('分镜顺序更新成功');
    } else {
      console.error('更新分镜顺序失败:', response.errMessage);
      ElMessage.error(`更新分镜顺序失败: ${response.errMessage}`);
      // 恢复原有顺序（通过重新获取画布详情）
      if (canvasId.value) {
        fetchCanvasDetail(canvasId.value);
      }
    }
  } catch (error) {
    console.error('更新分镜顺序异常:', error);
    ElMessage.error('更新分镜顺序出错，请稍后再试');
    // 恢复原有顺序（通过重新获取画布详情）
    if (canvasId.value) {
      fetchCanvasDetail(canvasId.value);
    }
  }
};
</script>

<style>
/* 全局覆盖 Element Plus 的 Switch 组件颜色 */
.el-switch.is-checked .el-switch__core {
  background-color: #706cefd7 !important;
  border-color: #706cefd7 !important;
}

.el-switch:hover:not(.is-disabled) .el-switch__core {
  border-color: #706cefd7 !important;
}
</style>

<style scoped>
.video-editor {
  width: 100vw;
  height: calc(100vh);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  /* background-color: #e6effd79; */
}

body.dark .video-editor {
  background-color: transparent !important;
  /* background-color: #000000; */
}

.video-editor-header {
  width: 100%;
  background-color: #FFF;
  border-bottom: 0.5px solid #83838316;
}

body.dark .video-editor-header {
  background-color: var(--bg-secondary-video);
}

.video-editor-layout {
  display: flex;
  width: 100%;
  flex: 1;
  overflow: hidden;
}

/* 左侧面板 */
.left-panel {
  width: 320px;
  /* height: 100%; */
  /* border-right: 1px solid #e4e7ed; */
  transition: width 0.3s ease;
  position: relative;
  /* overflow: hidden; */
  border-radius: 10px;
  box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.2);
  background-color: #ffffff;
  margin: 6px;
}

body.dark .left-panel {
  box-shadow: 0 0 1px 0 rgba(123, 123, 123, 0.322);
  background-color: var(--bg-secondary-video);
}

.left-panel.collapsed {
  width: 0px;
}

.left-panel.collapsed .panel-body {
  opacity: 0;
}

.panel-tabs {
  display: flex;
  flex: 1;
  box-sizing: border-box;
  overflow: hidden;
}

/* 右侧面板 */
.right-panel {
  width: 320px;
  height: 100%;
  /* border-left: 1px solid #e4e7ed; */
  transition: width 0.3s ease;
  position: relative;
  border-radius: 10px;
  box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.2);
  background-color: #ffffff;
  margin: 6px;
}

body.dark .right-panel {
  box-shadow: 0 0 1px 0 rgba(123, 123, 123, 0.322);
  background-color: var(--bg-secondary-video);
}

.right-panel.collapsed {
  width: 0px;
}

/* 中间面板 */
.center-panel {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  /* background-color: #fff; */
}

body.dark .center-panel {
  /* background-color: var(--bg-primary); */
}

/* 预览区域 */
.media-editor-menu-container {
  max-height: 80px;
  transition: all 0.3s ease-in-out, max-height 0.8s ease, opacity 0.5s ease;
  opacity: 1;
}

.media-editor-menu-container.collapsed {
  max-height: 0;
  opacity: 0;
  padding: 0;
  margin: 0;
}

.preview-area {
  flex: 1;
  min-height: 0;
  /* padding: 20px; */
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.preview-container {
  flex: 1;
  display: flex;
  overflow: hidden;
  border-radius: 8px;
  box-sizing: border-box;
}

/* 播放控制样式已移至 VideoPlaybackControls 组件中 */

/* 分镜列表区域 */
.shot-list-area {
  display: flex;
  flex-direction: column;
  border-radius: 10px 10px 0 0;
  box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.2);
  background-color: #ffffff;
  position: relative;
  /* max-height: 300px; */
  transition: all 0.3s ease-in-out, max-height 0.3s ease, padding 0.3s ease;
}

body.dark .shot-list-area {
  box-shadow: 0 0 1px 0 rgba(123, 123, 123, 0.322);
  background-color: var(--bg-secondary-video);
}

.shot-list-container {
  opacity: 1;
  transition: opacity 0.3s ease, height 0.3s ease-in-out;
}

.shot-list-area.collapsed {
  max-height: 0px;
  padding: 0;
}

.shot-list-area.collapsed .shot-list-container {
  opacity: 0;
}

.shot-list-area.collapsed .collapse-button.bottom {
  top: -10px;
}

/* 面板内容 */
.panel-content {
  width: 320px;
  height: 100%;
  padding: 0 0 0 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-sizing: border-box;
}

.panel-body {
  flex: 1;
  overflow-y: auto;
  transition: opacity 0.1s ease;
  display: flex;
  flex-direction: column;
}

/* 收起/展开按钮 */
.collapse-button {
  position: absolute;
  top: 50%;
  width: 20px;
  height: 40px;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
  border-radius: 40%;
}

body.dark .collapse-button {
  background-color: var(--bg-secondary-video);
  border-color: var(--border-color);
  color: var(--text-secondary);
}

.collapse-button:hover {
  background-color: #ecf5ff;
  color: #409eff;
}

body.dark .collapse-button:hover {
  background-color: var(--bg-tertiary);
  color: var(--primary-color);
}

.collapse-button.left {
  right: -10px;
  transform: translateY(-10%);
}

.collapse-button.right {
  left: -10px;
  transform: translateY(-10%);
}

.collapse-button.bottom {
  top: -12px;
  bottom: auto;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 18px;
}

/* 响应式布局 */
@media (max-width: 768px) {

  .left-panel:not(.collapsed),
  .right-panel:not(.collapsed) {
    width: 250px;
  }
}

/* 字幕开关样式已移至 VideoPlaybackControls 组件中 */
</style>